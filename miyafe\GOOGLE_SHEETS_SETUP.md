# Google Sheets Setup for CA Value

This guide explains how to set up Google Sheets to automatically update the CA value on your website.

## Method 1: Using Google Apps Script (Recommended)

### Step 1: Create a Google Sheet
1. Go to [Google Sheets](https://sheets.google.com)
2. Create a new spreadsheet
3. In cell A1, enter your CA value (e.g., "0x1234567890abcdef...")
4. Name your sheet something like "SORA CA Values"

### Step 2: Create Google Apps Script
1. In your Google Sheet, go to `Extensions` → `Apps Script`
2. Delete the default code and paste this:

```javascript
function doGet() {
  const sheet = SpreadsheetApp.getActiveSheet();
  const caValue = sheet.getRange('A1').getValue();
  
  const response = {
    ca_value: caValue,
    timestamp: new Date().toISOString()
  };
  
  return ContentService
    .createTextOutput(JSON.stringify(response))
    .setMimeType(ContentService.MimeType.JSON);
}
```

3. Save the script (Ctrl+S)
4. Click `Deploy` → `New deployment`
5. Choose type: `Web app`
6. Set execute as: `Me`
7. Set access: `Anyone` (or `Anyone with Google account` for more security)
8. Click `Deploy`
9. Copy the web app URL (it looks like: `https://script.google.com/macros/s/YOUR_SCRIPT_ID/exec`)

### Step 3: Update Environment Variable
1. Open `miyafe/.env`
2. Set `VITE_GOOGLE_SHEETS_URL` to your web app URL:
```
VITE_GOOGLE_SHEETS_URL=https://script.google.com/macros/s/YOUR_SCRIPT_ID/exec
```

## Method 2: Using Google Sheets API (More Complex)

### Step 1: Enable Google Sheets API
1. Go to [Google Cloud Console](https://console.cloud.google.com)
2. Create a new project or select existing
3. Enable Google Sheets API
4. Create credentials (API Key)
5. Make your sheet public or share with service account

### Step 2: Get Sheet ID and Range
1. Your sheet URL: `https://docs.google.com/spreadsheets/d/SHEET_ID/edit`
2. Extract the SHEET_ID from URL
3. Set the range (e.g., `Sheet1!A1`)

### Step 3: Update Environment Variable
```
VITE_GOOGLE_SHEETS_URL=https://sheets.googleapis.com/v4/spreadsheets/YOUR_SHEET_ID/values/Sheet1!A1?key=YOUR_API_KEY
```

## How It Works

1. **Auto-fetch**: The app automatically fetches the CA value when it loads
2. **Auto-refresh**: Updates every 5 minutes automatically
3. **Fallback**: If Google Sheets is unavailable, uses the fallback value from `VITE_CA_VALUE`
4. **Real-time updates**: Anyone with access to the Google Sheet can update the CA value

## Updating the CA Value

### For Method 1 (Apps Script):
1. Open your Google Sheet
2. Change the value in cell A1
3. The website will automatically pick up the new value within 5 minutes

### For Method 2 (API):
1. Open your Google Sheet
2. Change the value in the specified cell
3. The website will automatically pick up the new value within 5 minutes

## Security Considerations

1. **Public Access**: Method 1 with "Anyone" access means the URL is public
2. **Restricted Access**: Use "Anyone with Google account" for better security
3. **API Keys**: For Method 2, restrict your API key to specific domains
4. **Sheet Permissions**: Make sure only trusted people can edit the sheet

## Troubleshooting

### CA Value Not Updating
1. Check browser console for errors
2. Verify the Google Sheets URL is correct
3. Test the URL directly in browser
4. Check if the sheet is accessible

### CORS Issues
- Apps Script method (Method 1) handles CORS automatically
- API method (Method 2) might need CORS configuration

### Permission Errors
- Make sure the sheet is shared properly
- For Apps Script, ensure deployment permissions are correct

## Example Response Format

The Google Apps Script should return JSON in this format:
```json
{
  "ca_value": "0x1234567890abcdef...",
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

## Team Collaboration

Multiple team members can now update the CA value:
1. Share the Google Sheet with team members
2. Give them edit permissions
3. Anyone can update cell A1 with the new CA value
4. Changes appear on the website within 5 minutes

No need to redeploy or change code - just update the Google Sheet! 🚀
