<template>
  <div class="min-h-screen">
    <!-- Hero Section -->
    <div class="relative w-full h-screen overflow-hidden hero-background" style="border-radius: 0 0 96px 96px;">
      <!-- Background Video -->
      <video
        ref="backgroundVideo"
        src="/sorabackgroundfix.mp4"
        alt="City Background"
        class="absolute inset-0 w-full h-full object-cover"
        autoplay
        loop
        muted
        playsinline
        preload="auto"
        @loadeddata="handleVideoLoaded"
        @error="handleVideoError"
      />

      <!-- Fallback Background Image (shown if video fails) -->
      <div class="absolute inset-0 w-full h-full bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 fallback-bg"></div>
      
     <!-- Main Content -->
     <div class="relative z-10 h-full">
       <!-- Text Above Logo - positioned at the very top -->
       <div class="absolute top-16 left-1/2 transform -translate-x-1/2 text-center">
         <p class="text-center text-xl md:text-2xl text-white">Create Your Own Lo-Fi With</p>
       </div>

       <!-- Centered Content - higher position on mobile to avoid CA button overlap -->
       <div class="absolute top-1/3 sm:top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 sm:-translate-y-3/4 text-center">
         <!-- Logo Text - smaller on mobile, larger on desktop -->
         <h1 class="text-[28vw] sm:text-[20vw] md:text-[18vw] lg:text-[20vw] xl:text-[22vw] font-bold text-white leading-none tracking-wider">
           SORA
         </h1>
       </div>

       <!-- CA Text - responsive positioning to avoid overlap -->
       <div class="absolute bottom-80 sm:bottom-72 left-1/2 transform -translate-x-1/2 text-center">
         <div class="max-w-xs sm:max-w-none mx-auto">
           <button
             @click="copyToClipboard(caValue)"
             :disabled="isLoadingCA"
             class="px-4 py-2 sm:px-6 sm:py-3 bg-gradient-to-r from-white/20 to-white/10 backdrop-blur-md rounded-2xl text-white font-semibold text-base sm:text-lg md:text-xl hover:from-white/30 hover:to-white/20 transition-all duration-300 border border-white/40 hover:border-white/60 cursor-pointer shadow-xl hover:shadow-2xl transform hover:-translate-y-1 disabled:opacity-60 disabled:cursor-not-allowed disabled:hover:transform-none w-full sm:w-auto"
             :title="isLoadingCA ? 'Loading CA value...' : 'Click to copy CA value'"
           >
             <span v-if="isLoadingCA" class="flex items-center justify-center gap-2">
               <svg class="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                 <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                 <path class="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
               </svg>
               Loading...
             </span>
             <span v-else class="flex items-center justify-center gap-2">
               <span class="truncate sm:whitespace-nowrap sm:overflow-visible">
                 CA: {{ caValue }}
               </span>
               <svg class="w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
               </svg>
             </span>
           </button>
         </div>
       </div>

       <!-- Enhanced Input Section -->
       <div class="absolute bottom-12 left-0 right-0 px-4 z-20">
          <div class="max-w-4xl mx-auto">
            <form @submit.prevent="generateMusic">
              <!-- Input Container with Glass Effect -->
              <div class="bg-white/15 backdrop-blur-xl rounded-3xl p-3 sm:p-4 shadow-2xl border border-white/30 hover:bg-white/20 transition-all duration-300">
                <div class="flex flex-col sm:flex-row items-center gap-3">
                  <!-- Enhanced Input Field -->
                  <div class="flex-grow relative w-full">
                    <input
                      v-model="description"
                      type="text"
                      class="w-full pl-12 pr-4 py-3 sm:pl-14 sm:pr-8 sm:py-5 bg-white/95 backdrop-blur-sm rounded-2xl border-none focus:outline-none focus:ring-4 focus:ring-[#74C3C5]/30 text-gray-800 placeholder-gray-500 text-base sm:text-lg font-medium shadow-lg transition-all duration-300"
                      placeholder="Describe your perfect lo-fi vibe..."
                      required
                    />
                    <!-- Input Icon -->
                    <div class="absolute left-3 top-1/2 transform -translate-y-1/2">
                      <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"/>
                      </svg>
                    </div>
                  </div>

                  <!-- Enhanced Generate Button -->
                  <button
                    type="submit"
                    class="w-full sm:w-auto px-6 py-3 sm:px-8 sm:py-5 bg-gradient-to-r from-[#74C3C5] to-[#5ba0a2] text-white rounded-2xl text-base sm:text-lg font-bold hover:from-[#5ba0a2] hover:to-[#74C3C5] transition-all duration-300 disabled:opacity-60 disabled:cursor-not-allowed whitespace-nowrap shadow-xl hover:shadow-2xl transform hover:-translate-y-1 disabled:transform-none flex items-center justify-center gap-2 sm:gap-3"
                    :disabled="isGenerating"
                  >
                    <span v-if="!isGenerating" class="flex items-center gap-2">
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                      </svg>
                      <span class="whitespace-nowrap">Generate</span>
                    </span>
                    <span v-else class="flex items-center gap-2 sm:gap-3">
                      <div class="generate-loading"></div>
                      <span class="whitespace-nowrap">Creating...</span>
                    </span>
                  </button>
                </div>
              </div>

              <!-- Quick Suggestions -->
              <div class="mt-4 flex flex-wrap justify-center gap-2">
                <button
                  v-for="suggestion in quickSuggestions"
                  :key="suggestion"
                  type="button"
                  @click="description = suggestion"
                  class="px-4 py-2 bg-white/10 backdrop-blur-sm text-white text-sm rounded-full hover:bg-white/20 transition-all duration-300 border border-white/20 hover:border-white/40"
                >
                  {{ suggestion }}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content Area -->
    <div class="mt-4 bg-white" ref="resultSection">
      <!-- Result Section -->
      <MusicResult
        v-if="result?.cdnUrl"
        :result="result"
        @show-toast="showToast"
        @regenerate="generateMusic"
      />

      <!-- Empty State Placeholder -->
      <div v-else class="min-h-screen py-20">
        <div class="max-w-4xl mx-auto px-6 text-center">
          <!-- Placeholder Header -->
          <div class="mb-12">
            
            <h2 class="text-4xl font-bold text-gray-800 mb-4">Your Music Awaits</h2>
            <p class="text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
              Create your perfect lo-fi soundtrack above. Your generated music and video will appear here.
            </p>
          </div>

          <!-- Preview Cards -->
          <div class="grid md:grid-cols-2 gap-8 mb-12">
            <!-- Audio Player Preview -->
            <div class="bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-xl border border-gray-200/50">
              <div class="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-[#74C3C5]/20 to-[#5ba0a2]/20 rounded-2xl mx-auto mb-6">
                <svg class="w-8 h-8 text-[#74C3C5]" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M18 3a1 1 0 00-1.196-.98l-10 2A1 1 0 006 5v9.114A4.369 4.369 0 005 14c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V7.82l8-1.6v5.894A4.37 4.37 0 0015 12c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V3z"/>
                </svg>
              </div>
              <h3 class="text-2xl font-bold text-gray-800 mb-3">Audio Player</h3>
              <p class="text-gray-600 mb-6">High-quality lo-fi music with beautiful vinyl-inspired controls</p>
              <div class="bg-gray-100 rounded-2xl p-4">
                <div class="flex items-center gap-4">
                  <div class="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center">
                    <svg class="w-6 h-6 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"/>
                    </svg>
                  </div>
                  <div class="flex-1">
                    <div class="h-2 bg-gray-300 rounded-full mb-2"></div>
                    <div class="h-1 bg-gray-200 rounded-full"></div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Video Player Preview -->
            <div class="bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-xl border border-gray-200/50">
              <div class="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-[#74C3C5]/20 to-[#5ba0a2]/20 rounded-2xl mx-auto mb-6">
                <svg class="w-8 h-8 text-[#74C3C5]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                </svg>
              </div>
              <h3 class="text-2xl font-bold text-gray-800 mb-3">Video Player</h3>
              <p class="text-gray-600 mb-6">Convert your audio to video with beautiful anime-style visuals</p>
              <div class="bg-gray-100 rounded-2xl p-4 aspect-video flex items-center justify-center">
                <div class="text-center">
                  <svg class="w-12 h-12 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.01M15 10h1.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                  <p class="text-sm text-gray-500">Video preview</p>
                </div>
              </div>
            </div>
          </div>

          <!-- How It Works Section -->
          <div class="mt-16">
            <div class="text-center mb-12">
              <h3 class="text-3xl font-bold text-gray-800 mb-4">How It Works</h3>
              <p class="text-xl text-gray-600">Create amazing lo-fi music in 3 simple steps</p>
            </div>
            
            <!-- 3-step process -->
            <div class="grid md:grid-cols-3 gap-8 mb-16">
              <div class="text-center">
                <div class="w-16 h-16 bg-gradient-to-br from-[#74C3C5] to-[#5ba0a2] rounded-full flex items-center justify-center mx-auto mb-4">
                  <span class="text-2xl font-bold text-white">1</span>
                </div>
                <h4 class="text-xl font-bold mb-3 text-gray-800">Describe Your Vibe</h4>
                <p class="text-gray-600">Tell us the mood, setting, and atmosphere you want. Be specific about instruments, tempo, and scene.</p>
              </div>
              <div class="text-center">
                <div class="w-16 h-16 bg-gradient-to-br from-[#74C3C5] to-[#5ba0a2] rounded-full flex items-center justify-center mx-auto mb-4">
                  <span class="text-2xl font-bold text-white">2</span>
                </div>
                <h4 class="text-xl font-bold mb-3 text-gray-800">AI Creates Magic</h4>
                <p class="text-gray-600">Our open-source AI models analyze your prompt and generate unique, high-quality lo-fi music tailored to your description.</p>
              </div>
              <div class="text-center">
                <div class="w-16 h-16 bg-gradient-to-br from-[#74C3C5] to-[#5ba0a2] rounded-full flex items-center justify-center mx-auto mb-4">
                  <span class="text-2xl font-bold text-white">3</span>
                </div>
                <h4 class="text-xl font-bold mb-3 text-gray-800">Enjoy & Share</h4>
                <p class="text-gray-600">Listen to your personalized track, convert to video with anime visuals, and share your creation with the world.</p>
              </div>
            </div>
          </div>

          <!-- Prompt Writing Tips -->
          <div class="mt-16">
            <div class="text-center mb-12">
              <h3 class="text-3xl font-bold text-gray-800 mb-4">Writing Great Prompts</h3>
              <p class="text-xl text-gray-600">Tips to get the perfect lo-fi track</p>
            </div>
            
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              <!-- Be Specific Card -->
              <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 text-center">
                <div class="w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"/>
                  </svg>
                </div>
                <h4 class="font-bold text-lg mb-2 text-gray-800">Be Specific</h4>
                <p class="text-gray-600 text-sm mb-3">Use vivid descriptions instead of generic terms</p>
                <div class="text-xs text-gray-500 flex flex-wrap justify-center gap-1">
                  <span class="bg-gray-100 px-2 py-1 rounded">rainy cafe jazz</span>
                  <span class="bg-gray-100 px-2 py-1 rounded">cozy library</span>
                </div>
              </div>

              <!-- Musical Elements Card -->
              <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 text-center">
                <div class="w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <svg class="w-6 h-6 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M18 3a1 1 0 00-1.196-.98l-10 2A1 1 0 006 5v9.114A4.369 4.369 0 005 14c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V7.82l8-1.6v5.894A4.37 4.37 0 0015 12c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V3z"/>
                  </svg>
                </div>
                <h4 class="font-bold text-lg mb-2 text-gray-800">Include Instruments</h4>
                <p class="text-gray-600 text-sm mb-3">Mention specific instruments and tempo</p>
                <div class="text-xs text-gray-500 flex flex-wrap justify-center gap-1">
                  <span class="bg-gray-100 px-2 py-1 rounded">piano</span>
                  <span class="bg-gray-100 px-2 py-1 rounded">saxophone</span>
                </div>
              </div>

              <!-- Set Scene Card -->
              <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 text-center">
                <div class="w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064"/>
                  </svg>
                </div>
                <h4 class="font-bold text-lg mb-2 text-gray-800">Set the Scene</h4>
                <p class="text-gray-600 text-sm mb-3">Describe environment, time, and weather</p>
                <div class="text-xs text-gray-500 flex flex-wrap justify-center gap-1">
                  <span class="bg-gray-100 px-2 py-1 rounded">sunset beach</span>
                  <span class="bg-gray-100 px-2 py-1 rounded">midnight</span>
                </div>
              </div>

              <!-- Add Effects Card -->
              <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 text-center">
                <div class="w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"/>
                  </svg>
                </div>
                <h4 class="font-bold text-lg mb-2 text-gray-800">Add Sound Effects</h4>
                <p class="text-gray-600 text-sm mb-3">Include ambient sounds and effects</p>
                <div class="text-xs text-gray-500 flex flex-wrap justify-center gap-1">
                  <span class="bg-gray-100 px-2 py-1 rounded">vinyl crackle</span>
                  <span class="bg-gray-100 px-2 py-1 rounded">rain</span>
                </div>
              </div>

              <!-- Mood Card -->
              <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 text-center">
                <div class="w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                  </svg>
                </div>
                <h4 class="font-bold text-lg mb-2 text-gray-800">Express Mood</h4>
                <p class="text-gray-600 text-sm mb-3">Convey the emotional atmosphere</p>
                <div class="text-xs text-gray-500 flex flex-wrap justify-center gap-1">
                  <span class="bg-gray-100 px-2 py-1 rounded">nostalgic</span>
                  <span class="bg-gray-100 px-2 py-1 rounded">dreamy</span>
                </div>
              </div>

              <!-- Combine Elements Card -->
              <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 text-center">
                <div class="w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                  </svg>
                </div>
                <h4 class="font-bold text-lg mb-2 text-gray-800">Combine All</h4>
                <p class="text-gray-600 text-sm mb-3">Mix elements for best results</p>
                <div class="text-xs text-gray-500 flex flex-wrap justify-center gap-1">
                  <span class="bg-gray-100 px-2 py-1 rounded">scene + mood + instruments</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Interactive Examples Section -->
          <div class="mt-16 bg-gradient-to-r from-gray-50 to-gray-100 rounded-3xl p-8">
            <h3 class="text-2xl font-bold text-center mb-8 text-gray-800">Try These Popular Prompts</h3>
            <div class="grid md:grid-cols-2 gap-4">
              <button
                v-for="example in popularExamples"
                :key="example.prompt"
                @click="description = example.prompt; scrollToTop()"
                class="text-left p-4 bg-white rounded-xl hover:shadow-lg transition-all group hover:-translate-y-1"
              >
                <div>
                  <h4 class="font-semibold group-hover:text-[#74C3C5] transition-colors mb-2">{{ example.title }}</h4>
                  <p class="text-sm text-gray-600">{{ example.prompt }}</p>
                </div>
              </button>
            </div>
          </div>

          <!-- Technology Section -->
          <div class="mt-16 text-center">
            <div class="max-w-2xl mx-auto">
              <h3 class="text-2xl font-bold mb-4 text-gray-800">Powered by Open-Source AI</h3>
              <p class="text-gray-600 mb-8">Advanced AI models create unique, high-quality lo-fi music</p>
              
              <!-- Current capabilities -->
              <div class="bg-white rounded-2xl p-6 shadow-lg mb-6">
                <div class="flex items-center justify-between mb-3">
                  <span class="font-semibold text-gray-800">Instrumental Lo-Fi Generation</span>
                  <span class="text-green-600 font-semibold text-sm">Available Now</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-3">
                  <div class="bg-green-500 h-3 rounded-full w-full transition-all duration-500"></div>
                </div>
                <p class="text-sm text-gray-600 mt-2">Create unique instrumental lo-fi tracks with custom prompts</p>
              </div>
              
              <!-- Coming soon -->
              <div class="bg-gray-50 rounded-2xl p-6">
                <div class="flex items-center justify-between mb-3">
                  <span class="font-semibold text-gray-800">Custom Lyrics & Vocals</span>
                  <span class="text-blue-600 font-semibold text-sm">Coming Soon</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-3">
                  <div class="bg-blue-500 h-3 rounded-full w-3/4 transition-all duration-500"></div>
                </div>
                <p class="text-sm text-gray-600 mt-2">Full music generation with personalized words and melodies</p>
              </div>
              
              <p class="text-sm text-gray-500 mt-6">Each generation is unique and created just for you in real-time</p>
            </div>
          </div>

          <!-- Call to Action -->
          <div class="mt-16 bg-gradient-to-r from-[#74C3C5]/10 to-[#5ba0a2]/10 rounded-3xl p-8 border border-[#74C3C5]/20">
            <h3 class="text-2xl font-bold text-gray-800 mb-4">Ready to Create?</h3>
            <p class="text-gray-600 mb-6">Scroll up and describe your perfect lo-fi vibe to get started!</p>
            <button
              @click="scrollToTop"
              class="px-8 py-4 bg-gradient-to-r from-[#74C3C5] to-[#5ba0a2] text-white rounded-2xl font-semibold hover:from-[#5ba0a2] hover:to-[#74C3C5] transition-all duration-300 transform hover:-translate-y-1 shadow-lg hover:shadow-xl"
            >
              Start Creating Music
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Partners/Sponsors Section -->
    <section class="relative bg-gradient-to-br from-gray-900 via-black to-gray-900 py-16 overflow-hidden">
      <!-- Background Pattern -->
      <div class="absolute inset-0 opacity-5">
        <div class="floating-notes-partners">
          <div v-for="n in 8" :key="n" class="note-partner" :style="{ animationDelay: `${n * 1.2}s` }">♪</div>
        </div>
      </div>

      <!-- Gradient Overlay -->
      <div class="absolute inset-0 bg-gradient-to-t from-[#74C3C5]/5 via-transparent to-transparent"></div>

      <div class="relative z-10 max-w-6xl mx-auto px-6">
        <!-- Section Title -->
        <div class="text-center mb-12">
          <h3 class="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-[#74C3C5] via-white to-[#74C3C5] bg-clip-text text-transparent mb-4">
            Powered By Industry Leaders
          </h3>
          <p class="text-gray-400 text-lg max-w-2xl mx-auto">
            Built with cutting-edge technology from the world's most innovative companies
          </p>
        </div>

        <!-- Logo Grid -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8 items-center justify-items-center">
          <div class="partner-logo group">
            <img
              src="/aws.png"
              alt="AWS"
              class="h-12 sm:h-16 w-auto opacity-70 group-hover:opacity-100 transition-all duration-300 filter grayscale group-hover:grayscale-0 group-hover:scale-110"
            />
          </div>
          <div class="partner-logo group">
            <img
              src="/intel.png"
              alt="Intel"
              class="h-12 sm:h-16 w-auto opacity-70 group-hover:opacity-100 transition-all duration-300 filter grayscale group-hover:grayscale-0 group-hover:scale-110"
            />
          </div>
          <div class="partner-logo group">
            <img
              src="/nvidia.png"
              alt="NVIDIA"
              class="h-12 sm:h-16 w-auto opacity-70 group-hover:opacity-100 transition-all duration-300 filter grayscale group-hover:grayscale-0 group-hover:scale-110"
            />
          </div>
          <div class="partner-logo group">
            <img
              src="/sony.png"
              alt="Sony"
              class="h-12 sm:h-16 w-auto opacity-70 group-hover:opacity-100 transition-all duration-300 filter grayscale group-hover:grayscale-0 group-hover:scale-110"
            />
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <Footer />

    <!-- Loading Modal -->
    <LoadingModal :is-open="isGenerating" />

    <!-- Toast -->
    <ToastNotification :toast="toast" />
  </div>
</template>

<script setup>
import { ref, watch, nextTick } from 'vue'
import MusicResult from './components/MusicResult.vue'
import LoadingModal from './components/LoadingModal.vue'
import ToastNotification from './components/ToastNotification.vue'
import Footer from './components/Footer.vue'

const description = ref('')
const isGenerating = ref(false)
const result = ref(null)
const toast = ref(null)
const resultSection = ref(null)
const backgroundVideo = ref(null)
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL

// CA Value from backend
const caValue = ref(import.meta.env.VITE_CA_VALUE || 'Loading...')
const isLoadingCA = ref(false)

// Quick suggestion prompts
const quickSuggestions = ref([
  'rainy night jazz cafe',
  'sunset beach vibes',
  'cozy study session',
  'midnight city drive',
  'forest morning calm',
  'vintage vinyl warmth'
])

// Popular example prompts for interactive section
const popularExamples = ref([
  {
    title: 'Cozy Study Session',
    prompt: 'warm lo-fi with soft piano, gentle rain sounds, and fireplace crackles for focused studying'
  },
  {
    title: 'Midnight City Drive',
    prompt: 'chillhop with smooth bass, mellow saxophone, and distant traffic sounds for late night vibes'
  },
  {
    title: 'Forest Morning Calm',
    prompt: 'ambient lo-fi with birds chirping, gentle wind through trees, and soft acoustic guitar'
  },
  {
    title: 'Vintage Vinyl Warmth',
    prompt: 'old-school jazz lo-fi with record player warmth, vinyl crackle, and nostalgic piano melodies'
  },
  {
    title: 'Sunset Beach Vibes',
    prompt: 'relaxing lo-fi with acoustic guitar, gentle ocean waves, light breeze, and distant seagulls'
  },
  {
    title: 'Rainy Cafe Jazz',
    prompt: 'cozy jazz cafe atmosphere with soft piano, gentle rain on windows, and warm coffee shop ambience'
  },
  {
    title: 'Winter Evening Cabin',
    prompt: 'peaceful cabin vibes with crackling fireplace, soft acoustic guitar, and gentle snowfall outside'
  },
  {
    title: 'Tokyo Night Streets',
    prompt: 'urban lo-fi with distant city sounds, neon-lit atmosphere, and dreamy synthesizer melodies'
  }
])

// Function to obfuscate payload
function obfuscatePayload(data) {
  // Convert to base64 and add some random padding
  const jsonString = JSON.stringify(data)
  const base64 = btoa(jsonString)
  const randomPrefix = Math.random().toString(36).substring(2, 8)
  const randomSuffix = Math.random().toString(36).substring(2, 8)
  return `${randomPrefix}${base64}${randomSuffix}`
}

async function generateMusic() {
  isGenerating.value = true
  result.value = null

  try {
    // Original payload (this won't be visible in network tab)
    const originalPayload = {
      model: "suno",
      task_type: "generate_music",
      input: {
        model_version: 'chirp-v3-5',
        gpt_description_prompt: `lofi ${description.value.trim()}`,
        make_instrumental: true
      }
    }

    // Create obfuscated request body
    const requestBody = {
      d: obfuscatePayload(originalPayload),
      t: Date.now(),
      v: "1.0"
    }

    const response = await fetch(`${API_BASE_URL}/api/music`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(requestBody)
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error?.message || `Request failed with status ${response.status}`)
    }

    const data = await response.json()
    
    if (data.error?.message) {
      throw new Error(data.error.message)
    }

    // Start polling for task status
    await pollTaskStatus(data.data.task_id)

  } catch (error) {
    console.error('Error:', error)
    showToast(error.message, 'error')
    isGenerating.value = false
  }
}

async function pollTaskStatus(taskId) {
  const maxAttempts = 60 // 10 minutes maximum (10 second intervals)
  let attempts = 0
  let musicCompleted = false

  const poll = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/music?taskId=${taskId}`, {
        headers: {
          'Accept': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error(`Status check failed with status ${response.status}`)
      }

      const data = await response.json()

      if (data.error?.message) {
        throw new Error(data.error.message)
      }

      const status = data.status.toLowerCase()
      
      // If we have audio URL, update the result
      if (data.data?.audio_url) {
        if (!result.value) {
          result.value = {
            cdnUrl: data.data.audio_url,
            video_status: null,
            video_url: null
          }
        } else {
          result.value.cdnUrl = data.data.audio_url
        }

        // If music generation is complete
        if (status === 'completed' && !musicCompleted) {
          musicCompleted = true
          showToast('Your lo-fi masterpiece is ready! Scroll down to enjoy your exclusive track.', 'success')
          isGenerating.value = false
          return // Stop polling once music is complete
        }
      }

      if (status === 'failed') {
        throw new Error(data.error?.message || 'Music generation failed. Please try again.')
      }

      // Continue polling only if music not completed and haven't reached max attempts
      if (!musicCompleted && attempts < maxAttempts) {
        attempts++
        setTimeout(poll, 10000) // Poll every 10 seconds
      } else if (!musicCompleted) {
        throw new Error('Generation timed out. Please try again.')
      }
    } catch (error) {
      console.error('Polling error:', error)
      showToast(error.message, 'error')
      isGenerating.value = false
    }
  }

  await poll()
}

function showToast(message, type = 'info') {
  toast.value = { message, type }
  setTimeout(() => {
    toast.value = null
  }, 5000)
}

function scrollToTop() {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}

function copyToClipboard(text) {
  navigator.clipboard.writeText(text).then(() => {
    showToast('CA number copied to clipboard!', 'success')
  }).catch(() => {
    showToast('Failed to copy to clipboard', 'error')
  })
}

// Function to fetch CA value from backend
async function fetchCAValue() {
  isLoadingCA.value = true

  try {
    const response = await fetch(`${API_BASE_URL}/api/ca-value`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch CA value: ${response.status}`)
    }

    const data = await response.json()

    if (data && data.ca_value) {
      caValue.value = data.ca_value
    }

  } catch (error) {
    console.error('Error fetching CA from backend:', error)
    // Keep the current value or use env fallback
    if (caValue.value === 'Loading...') {
      caValue.value = import.meta.env.VITE_CA_VALUE || 'Coming Soon!'
    }
  } finally {
    isLoadingCA.value = false
  }
}

// Auto-refresh CA value every 5 minutes
function startCAAutoRefresh() {
  // Fetch immediately on load
  fetchCAValue()

  // Then refresh every 5 minutes (300000 ms)
  setInterval(fetchCAValue, 300000)
}

// Handle background video loading
function handleVideoLoaded() {
  console.log('Background video loaded successfully')
  // Force play for iOS devices
  if (backgroundVideo.value) {
    backgroundVideo.value.play().catch(error => {
      console.log('Video autoplay failed:', error)
      // Try again after user interaction
      document.addEventListener('touchstart', () => {
        if (backgroundVideo.value) {
          backgroundVideo.value.play().catch(e => console.log('Manual play failed:', e))
        }
      }, { once: true })
    })
  }
}

function handleVideoError(error) {
  console.error('Background video error:', error)
  // Fallback: hide video and show animated background
  if (backgroundVideo.value) {
    backgroundVideo.value.style.display = 'none'
    // Add class to show fallback background
    const heroSection = backgroundVideo.value.closest('.hero-background')
    if (heroSection) {
      heroSection.classList.add('video-error')
    }
  }
}

// Watch for when result becomes available
watch(() => result.value?.cdnUrl, (newUrl) => {
  if (newUrl) {
    // Wait for next tick to ensure component is rendered
    nextTick(() => {
      resultSection.value?.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      })
    })
  }
})

// Start CA auto-refresh when component mounts
startCAAutoRefresh()
</script>

<style scoped>
/* Enhanced Loading Animation for Generate Button */
.generate-loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: generate-spin 1s ease-in-out infinite;
}

@keyframes generate-spin {
  to {
    transform: rotate(360deg);
  }
}

/* Input Focus Effects */
input:focus {
  transform: translateY(-1px);
}

/* Suggestion Button Hover Effects */
button[type="button"]:hover {
  transform: translateY(-1px);
}

/* Glass Effect Enhancement */
.bg-white\/15 {
  background: rgba(255, 255, 255, 0.15);
}

.bg-white\/10 {
  background: rgba(255, 255, 255, 0.1);
}

.bg-white\/20 {
  background: rgba(255, 255, 255, 0.2);
}

/* Responsive Adjustments */
@media (max-width: 640px) {
  .absolute.bottom-12 {
    bottom: 2rem;
  }

  .absolute.top-16 {
    top: 3rem;
  }
}

/* Enhanced Shadow Effects */
.shadow-2xl {
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* Background Video Fallback */
.hero-background {
  position: relative;
}

.fallback-bg {
  opacity: 0;
  transition: opacity 0.5s ease-in-out;
  background: linear-gradient(135deg, #1e293b 0%, #581c87 50%, #1e293b 100%);
  background-size: 400% 400%;
  animation: gradient-shift 8s ease-in-out infinite;
}

.hero-background.video-error .fallback-bg {
  opacity: 1;
}

@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Partners Section Styles */
.floating-notes-partners {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.note-partner {
  position: absolute;
  font-size: 24px;
  color: #74C3C5;
  animation: float-partner 20s infinite linear;
  opacity: 0.08;
}

.note-partner:nth-child(1) { left: 10%; animation-duration: 18s; }
.note-partner:nth-child(2) { left: 25%; animation-duration: 22s; }
.note-partner:nth-child(3) { left: 40%; animation-duration: 16s; }
.note-partner:nth-child(4) { left: 55%; animation-duration: 24s; }
.note-partner:nth-child(5) { left: 70%; animation-duration: 20s; }
.note-partner:nth-child(6) { left: 85%; animation-duration: 19s; }
.note-partner:nth-child(7) { left: 15%; animation-duration: 21s; }
.note-partner:nth-child(8) { left: 75%; animation-duration: 17s; }

@keyframes float-partner {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 0.08;
  }
  90% {
    opacity: 0.08;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}

.partner-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.partner-logo:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(116, 195, 197, 0.15);
}

/* Backdrop Blur Fallback */
@supports not (backdrop-filter: blur(12px)) {
  .backdrop-blur-xl {
    background: rgba(255, 255, 255, 0.25);
  }

  .backdrop-blur-md {
    background: rgba(255, 255, 255, 0.2);
  }

  .backdrop-blur-sm {
    background: rgba(255, 255, 255, 0.15);
  }
}
</style>
