<template>
  <footer class="relative bg-gradient-to-br from-gray-900 via-black to-gray-900 py-16 mt-20 overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-5">
      <div class="floating-notes-footer">
        <div v-for="n in 15" :key="n" class="note-footer" :style="{ animationDelay: `${n * 0.8}s` }">♪</div>
      </div>
    </div>

    <!-- Gradient Overlay -->
    <div class="absolute inset-0 bg-gradient-to-t from-[#74C3C5]/10 via-transparent to-transparent"></div>

    <div class="relative z-10 max-w-6xl mx-auto px-6">
      <!-- Main Footer Content -->
      <div class="text-center mb-12">
        <!-- Logo Section -->
        <div class="mb-8">
          <div class="inline-flex items-center justify-center w-16 h-16 mb-4">
            <img src="/Sora-Favicon.png" alt="Sora Music Logo" class="w-16 h-16 rounded-2xl shadow-xl">
          </div>
          <h3 class="text-3xl font-bold bg-gradient-to-r from-[#74C3C5] via-white to-[#74C3C5] bg-clip-text text-transparent mb-2">
            Sora Music
          </h3>
         
        </div>

        <!-- Description -->
        <p class="text-gray-300 text-lg max-w-2xl mx-auto mb-10 leading-relaxed">
          Create your perfect lo-fi soundtrack with AI. Transform your imagination into
          <span class="text-[#74C3C5] font-semibold">beautiful melodies</span> that capture every mood and moment.
        </p>

        <!-- Social Links -->
        <div class="flex justify-center items-center space-x-6 mb-10">
          <a
            href="https://twitter.com/miyabimusic_"
            target="_blank"
            rel="noopener noreferrer"
            class="social-link group"
          >
            <div class="social-icon bg-white">
              <svg class="w-6 h-6 text-black transition-transform group-hover:scale-110" fill="currentColor" viewBox="0 0 24 24">
                <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
              </svg>
            </div>
            <span class="social-label">Twitter</span>
          </a>

          <a
            href="https://t.me/miyabimusic"
            target="_blank"
            rel="noopener noreferrer"
            class="social-link group"
          >
            <div class="social-icon bg-white">
              <svg class="w-6 h-6 text-black transition-transform group-hover:scale-110" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm5.894 8.221l-1.97 9.28c-.145.658-.537.818-1.084.508l-3-2.21-1.446 1.394c-.14.18-.357.223-.548.223l.188-2.85 5.18-4.685c.223-.198-.054-.308-.346-.11l-6.4 4.02-2.76-.918c-.6-.187-.612-.6.125-.89l10.782-4.156c.5-.18.94.12.78.88z"/>
              </svg>
            </div>
            <span class="social-label">Telegram</span>
          </a>
        </div>
      </div>

      <!-- Bottom Section -->
      <div class="border-t border-gray-800 pt-8">
        <div class="flex flex-col md:flex-row justify-between items-center gap-4">
          <div class="text-gray-400 text-sm">
          </div>
          <div class="flex items-center gap-6 text-sm text-gray-400">
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup>
// No script needed for now
</script>

<style scoped>
/* Floating Musical Notes Background */
.floating-notes-footer {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.note-footer {
  position: absolute;
  font-size: 28px;
  color: #74C3C5;
  animation: float-footer 15s infinite linear;
  opacity: 0.1;
}

.note-footer:nth-child(1) { left: 5%; animation-duration: 12s; }
.note-footer:nth-child(2) { left: 15%; animation-duration: 18s; }
.note-footer:nth-child(3) { left: 25%; animation-duration: 14s; }
.note-footer:nth-child(4) { left: 35%; animation-duration: 16s; }
.note-footer:nth-child(5) { left: 45%; animation-duration: 13s; }
.note-footer:nth-child(6) { left: 55%; animation-duration: 17s; }
.note-footer:nth-child(7) { left: 65%; animation-duration: 15s; }
.note-footer:nth-child(8) { left: 75%; animation-duration: 19s; }
.note-footer:nth-child(9) { left: 85%; animation-duration: 11s; }
.note-footer:nth-child(10) { left: 95%; animation-duration: 20s; }
.note-footer:nth-child(11) { left: 10%; animation-duration: 14s; }
.note-footer:nth-child(12) { left: 30%; animation-duration: 16s; }
.note-footer:nth-child(13) { left: 50%; animation-duration: 12s; }
.note-footer:nth-child(14) { left: 70%; animation-duration: 18s; }
.note-footer:nth-child(15) { left: 90%; animation-duration: 15s; }

@keyframes float-footer {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 0.1;
  }
  90% {
    opacity: 0.1;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}

/* Social Link Styles */
.social-link {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.social-link:hover {
  transform: translateY(-4px);
}

.social-icon {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 8px 16px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.social-link:hover .social-icon {
  box-shadow:
    0 12px 24px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.social-label {
  color: #9ca3af;
  font-size: 12px;
  font-weight: 500;
  transition: color 0.3s ease;
}

.social-link:hover .social-label {
  color: #74C3C5;
}

/* Responsive Design */
@media (max-width: 768px) {
  .social-link {
    gap: 6px;
  }

  .social-icon {
    width: 48px;
    height: 48px;
  }

  .social-icon svg {
    width: 20px;
    height: 20px;
  }

  .social-label {
    font-size: 11px;
  }
}

/* Enhanced Gradient Text */
.bg-gradient-to-r {
  background-size: 200% 200%;
  animation: gradient-shift 3s ease-in-out infinite;
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Link Hover Effects */
a[href="#"]:hover {
  text-decoration: none;
  transform: translateY(-1px);
}

/* Border Animation */
.border-t {
  position: relative;
}

.border-t::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, #74C3C5, transparent);
  animation: border-expand 2s ease-in-out infinite;
}

@keyframes border-expand {
  0%, 100% {
    width: 0;
  }
  50% {
    width: 200px;
  }
}
</style>
