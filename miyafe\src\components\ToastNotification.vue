<template>
  <Transition
    enter-active-class="transition duration-300 ease-out"
    enter-from-class="transform translate-y-2 opacity-0"
    enter-to-class="transform translate-y-0 opacity-100"
    leave-active-class="transition duration-200 ease-in"
    leave-from-class="transform translate-y-0 opacity-100"
    leave-to-class="transform translate-y-2 opacity-0"
  >
    <div
      v-if="toast"
      class="fixed bottom-4 right-4 px-4 py-3 rounded-xl shadow-lg z-50 flex items-center gap-3 min-w-300 max-w-md text-white"
      :class="{
        'bg-red-500': toast.type === 'error',
        'bg-gray-800': !toast.type
      }"
      :style="{
        background: toast.type === 'success' ? 'linear-gradient(135deg, #74C3C5, #5ba0a2)' :
                   toast.type === 'info' ? 'linear-gradient(135deg, #74C3C5, #5ba0a2)' : ''
      }"
    >
      <!-- Icons -->
      <div class="flex-shrink-0">
        <!-- Success Icon -->
        <svg v-if="toast.type === 'success'" class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        
        <!-- Error Icon -->
        <svg v-if="toast.type === 'error'" class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        
        <!-- Info Icon -->
        <svg v-if="toast.type === 'info'" class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
      </div>

      <!-- Message -->
      <div class="flex-1">
        <p class="font-medium">{{ toast.message }}</p>
      </div>

      <!-- Progress Bar -->
      <div class="absolute bottom-0 left-0 h-1 bg-white bg-opacity-20 transition-all duration-300" :style="{ width: `${progress}%` }"></div>
    </div>
  </Transition>
</template>

<script setup>
import { ref, watch, onUnmounted } from 'vue'

const props = defineProps({
  toast: {
    type: Object,
    default: null,
    validator: (value) => {
      if (!value) return true
      return typeof value.message === 'string'
    }
  }
})

const progress = ref(100)
let progressInterval

// Watch for toast changes
watch(() => props.toast, (newToast) => {
  if (newToast) {
    // Reset and start progress
    progress.value = 100
    clearInterval(progressInterval)
    
    progressInterval = setInterval(() => {
      if (progress.value > 0) {
        progress.value -= 2 // Decrease by 2% every 100ms
      }
    }, 100)
  } else {
    clearInterval(progressInterval)
  }
})

onUnmounted(() => {
  clearInterval(progressInterval)
})
</script>

<style scoped>
.min-w-300 {
  min-width: 300px;
}
</style>
