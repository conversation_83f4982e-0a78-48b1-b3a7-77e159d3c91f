require('dotenv').config();
const express = require('express');
const axios = require('axios');
const ffmpeg = require('fluent-ffmpeg');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const winston = require('winston');
const { S3Client } = require('@aws-sdk/client-s3');
const { Upload } = require('@aws-sdk/lib-storage');
const crypto = require('crypto');
const Replicate = require('replicate');

// Set FFmpeg path from the installed package
try {
    const ffmpegPath = require('@ffmpeg-installer/ffmpeg').path;
    ffmpeg.setFfmpegPath(ffmpegPath);
    console.log('FFmpeg path set to:', ffmpegPath);
} catch (error) {
    console.warn('Could not set FFmpeg path from @ffmpeg-installer/ffmpeg:', error.message);
}

// Initialize Replicate client
const replicate = new Replicate({
    auth: process.env.REPLICATE_API_TOKEN,
});

// Configure logging
const logger = winston.createLogger({
    level: 'debug',
    format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
    ),
    transports: [
        new winston.transports.Console()
    ]
});

const app = express();
const PORT = process.env.PORT || 8080;

// Verify API key
const SUNO_API_KEY = process.env.SUNO_API;
if (!SUNO_API_KEY) {
    throw new Error("SUNO_API environment variable is not set");
}

// Create temp directory
const TEMP_DIR = path.join(__dirname, 'temp');
if (!fs.existsSync(TEMP_DIR)) {
    fs.mkdirSync(TEMP_DIR);
}

// Configure S3 client for DO Spaces
const s3Client = new S3Client({
    endpoint: process.env.DO_SPACES_ENDPOINT,
    region: "sgp1",
    credentials: {
        accessKeyId: process.env.DO_SPACES_KEY,
        secretAccessKey: process.env.DO_SPACES_SECRET
    }
});

// Basic middleware
app.use(express.json());
app.use(cors());

// Suno API callback endpoint
app.post('/api/suno-callback', (req, res) => {
    logger.info('[Suno Callback] Received callback:', JSON.stringify(req.body, null, 2));
    res.json({ status: 'received' });
});

// Download audio endpoint
app.get('/api/download-audio', async (req, res) => {
    const { url } = req.query;
    if (!url) {
        return res.status(400).json({ error: 'URL is required' });
    }

    try {
        logger.info(`[Download Audio] Downloading from URL: ${url}`);

        // Download the file
        const response = await axios({
            method: 'get',
            url: url,
            responseType: 'stream',
            timeout: 30000 // 30 seconds timeout
        });

        // Get original filename or use default
        const filename = url.split('/').pop() || 'sora-lofi.mp3';
        
        // Set headers for download
        res.setHeader('Content-Type', 'audio/mpeg');
        res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
        res.setHeader('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
        
        // Pipe the file stream to response
        response.data.pipe(res);
    } catch (error) {
        logger.error('[Download Audio] Error:', error);
        res.status(500).json({ 
            error: 'Failed to download audio file',
            details: error.message
        });
    }
});

// Logging middleware
app.use((req, res, next) => {
    logger.debug(`Incoming request: ${req.method} ${req.path}`);
    next();
});

// Task status storage with auto-cleanup
const taskStatus = new Map();

// Check if ffmpeg is installed
async function checkFfmpeg() {
    return new Promise((resolve) => {
        ffmpeg.getAvailableCodecs((err, codecs) => {
            if (err) {
                logger.error('FFmpeg check failed:', err);
                resolve(false);
            } else {
                resolve(true);
            }
        });
    });
}

// Helper function for HTTP requests
async function fetchWithErrorHandling(url, method = 'GET', headers = null, body = null) {
    try {
        const response = await axios({
            method,
            url,
            headers,
            data: body,
            timeout: 30000
        });
        return response.data;
    } catch (error) {
        logger.error(`API Error: ${error.message}`);
        if (error.response) {
            const data = error.response.data;
            if (error.response.status === 503 && data.message?.toLowerCase().includes('maintenance')) {
                throw { status: 503, message: 'Service under maintenance. Please try again later.' };
            }
            throw { status: error.response.status, message: data.message || `Request failed (${error.response.status})` };
        }
        throw { status: 500, message: error.message };
    }
}

// Helper function to upload to DO Spaces
async function uploadToSpaces(filePath, contentType) {
    const fileName = `${crypto.randomBytes(8).toString('hex')}_${path.basename(filePath)}`;
    logger.info(`[DO Spaces] Starting upload: ${fileName}`);

    if (!fs.existsSync(filePath)) {
        throw new Error(`File not found: ${filePath}`);
    }

    const fileStream = fs.createReadStream(filePath);
    const upload = new Upload({
        client: s3Client,
        params: {
            Bucket: process.env.DO_SPACES_BUCKET,
            Key: fileName,
            Body: fileStream,
            ContentType: contentType,
            ACL: 'public-read'
        }
    });

    try {
        await upload.done();
        return `${process.env.DO_SPACES_ENDPOINT}/${process.env.DO_SPACES_BUCKET}/${fileName}`;
    } catch (error) {
        logger.error(`[DO Spaces] Upload error:`, error);
        throw error;
    }
}

// Download and upload to DO Spaces
async function downloadAndUploadToSpaces(url) {
    const tempPath = path.join(TEMP_DIR, `temp_${Date.now()}.mp3`);
    
    try {
        // Download file
        const response = await axios({
            method: 'GET',
            url: url,
            responseType: 'stream',
            timeout: 30000
        });

        // Save to temp file
        await new Promise((resolve, reject) => {
            const writer = fs.createWriteStream(tempPath);
            response.data.pipe(writer);
            writer.on('finish', resolve);
            writer.on('error', reject);
        });

        // Upload to DO Spaces
        const cdnUrl = await uploadToSpaces(tempPath, 'audio/mpeg');
        
        // Cleanup
        fs.unlinkSync(tempPath);
        
        return cdnUrl;
    } catch (error) {
        if (fs.existsSync(tempPath)) {
            fs.unlinkSync(tempPath);
        }
        throw error;
    }
}

// Helper function to update task status
function updateTaskStatus(taskId, status, progress, message, videoUrl = null) {
    const update = {
        status,
        progress,
        message,
        timestamp: Date.now()
    };
    if (videoUrl) update.videoUrl = videoUrl;
    taskStatus.set(taskId, update);
    logger.info(`[Task Status] ${taskId}: ${status} (${progress}%) - ${message}`);
}

// Helper function to get a random image path
function getRandomImagePath() {
    const randomNumber = Math.floor(Math.random() * 10) + 1; // 1-10
    return path.join(__dirname, 'public', `${randomNumber}.png`);
}

// Enhanced video conversion function
async function processAudioToVideo(audioUrl, taskId) {
    const audioPath = path.join(TEMP_DIR, `${taskId}_audio.mp3`);
    const outputPath = path.join(TEMP_DIR, `${taskId}_output.mp4`);
    const imagePath = getRandomImagePath();

    try {
        // Update initial status
        updateTaskStatus(taskId, 'processing', 0, 'Starting audio download');

        // Download audio
        const audioResponse = await axios({
            method: 'GET',
            url: audioUrl,
            responseType: 'stream',
            timeout: 30000
        });

        await new Promise((resolve, reject) => {
            const writer = fs.createWriteStream(audioPath);
            audioResponse.data.pipe(writer);
            writer.on('finish', resolve);
            writer.on('error', reject);
        });

        // Verify files
        if (!fs.existsSync(audioPath)) throw new Error('Audio download failed');
        if (!fs.existsSync(imagePath)) throw new Error('Background image not found');

        updateTaskStatus(taskId, 'processing', 20, 'Starting video conversion');

        // Convert to video
        await new Promise((resolve, reject) => {
            let lastProgress = 0;
            ffmpeg()
                .input(imagePath)
                .inputOptions(['-loop 1'])
                .input(audioPath)
                .outputOptions([
                    '-c:v libx264',
                    '-preset medium',
                    '-crf 23',
                    '-c:a aac',
                    '-b:a 192k',
                    '-b:v 2500k',
                    '-r 30',
                    '-s 1280x720',
                    '-pix_fmt yuv420p',
                    '-movflags +faststart',
                    '-shortest'
                ])
                .on('progress', (progress) => {
                    const percent = Math.min(Math.floor(progress.percent) || 0, 100);
                    if (percent > lastProgress) {
                        lastProgress = percent;
                        updateTaskStatus(taskId, 'processing', 20 + (percent * 0.6), `Converting video: ${percent}%`);
                    }
                })
                .on('end', resolve)
                .on('error', (err) => {
                    logger.error('[FFmpeg] Error:', err);
                    reject(err);
                })
                .save(outputPath);
        });

        // Verify output
        if (!fs.existsSync(outputPath)) throw new Error('Video conversion failed');

        updateTaskStatus(taskId, 'processing', 80, 'Uploading to storage');

        // Upload to DO Spaces
        const videoUrl = await uploadToSpaces(outputPath, 'video/mp4');
        
        // Cleanup
        await cleanupTempFiles(audioPath, outputPath);
        
        updateTaskStatus(taskId, 'completed', 100, 'Video ready', videoUrl);
        return videoUrl;
    } catch (error) {
        logger.error('[Video Conversion] Error:', error);
        updateTaskStatus(taskId, 'failed', 0, error.message);
        await cleanupTempFiles(audioPath, outputPath);
        throw error;
    }
}

// Cleanup function
async function cleanupTempFiles(...files) {
    for (const file of files) {
        try {
            if (fs.existsSync(file)) {
                await fs.promises.unlink(file);
                logger.info(`Cleaned up: ${file}`);
            }
        } catch (error) {
            logger.error(`Cleanup error for ${file}:`, error);
        }
    }
}

// API Endpoints

// Root endpoint
app.get('/', (req, res) => {
    res.json({ status: 'ok', message: 'Sora Music API' });
});

// CA Value endpoint - proxy to Google Sheets
app.get('/api/ca-value', async (req, res) => {
    logger.info('[CA Value] Fetching from Google Sheets');

    try {
        const googleSheetsUrl = process.env.GOOGLE_SHEETS_URL;

        if (!googleSheetsUrl) {
            logger.warn('[CA Value] Google Sheets URL not configured');
            return res.json({
                ca_value: process.env.CA_VALUE || 'Coming Soon!'
            });
        }

        const response = await fetch(googleSheetsUrl, {
            method: 'GET',
            headers: {
                'Accept': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`Google Sheets request failed: ${response.status}`);
        }

        const data = await response.json();

        if (data && data.ca_value) {
            logger.info('[CA Value] Successfully fetched from Google Sheets:', data.ca_value);
            res.json({
                ca_value: data.ca_value
            });
        } else {
            throw new Error('Invalid response format from Google Sheets');
        }

    } catch (error) {
        logger.error('[CA Value] Error fetching from Google Sheets:', error.message);

        // Return fallback value
        res.json({
            ca_value: process.env.CA_VALUE || 'Coming Soon!'
        });
    }
});

// Function to deobfuscate payload
function deobfuscatePayload(obfuscatedData) {
    try {
        // Remove random prefix (6 chars) and suffix (6 chars)
        const base64Data = obfuscatedData.substring(6, obfuscatedData.length - 6);
        // Decode base64
        const jsonString = Buffer.from(base64Data, 'base64').toString('utf-8');
        // Parse JSON
        return JSON.parse(jsonString);
    } catch (error) {
        logger.error('[Deobfuscation] Error:', error);
        throw new Error('Invalid payload format');
    }
}

// Generate music endpoint
app.post('/api/music', async (req, res) => {
    logger.info('[Music Generation] Starting new task');
    try {
        let actualPayload;

        // Check if payload is obfuscated (new format) or direct (old format for backward compatibility)
        if (req.body.d && req.body.t && req.body.v) {
            // New obfuscated format
            actualPayload = deobfuscatePayload(req.body.d);
            logger.info('[Music Generation] Using obfuscated payload');
        } else {
            // Old direct format (backward compatibility)
            actualPayload = req.body;
            logger.info('[Music Generation] Using direct payload (legacy)');
        }

        // Extract prompt from actual payload
        const userPrompt = actualPayload.input.gpt_description_prompt || '';

        // Add lofi pre-prompt and ensure instrumental
        const lofiPrompt = `lofi ${userPrompt} instrumental, chill, relaxing, study music, no vocals, no lyrics, smooth jazz hop, ambient`;
        
        logger.info('[Suno API] Starting generation with prompt: ' + lofiPrompt);

        // Start generation with Suno API
        const response = await axios.post('https://api.sunoapi.org/api/v1/generate', {
            prompt: lofiPrompt,
            style: "lofi, chill, instrumental, ambient, jazz hop",
            title: "Lo-Fi Chill Track",
            customMode: true,
            instrumental: true,
            model: "V3_5",
            negativeTags: "vocals, lyrics, singing, rap, rock, pop, metal, electronic, techno, upbeat drums",
            callBackUrl: `http://localhost:${PORT}/api/suno-callback`
        }, {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${SUNO_API_KEY}`
            }
        });

        // Debug: Log full response to understand structure
        logger.debug('[Suno API] Full response:', JSON.stringify(response.data, null, 2));

        // Handle different response structures
        let taskId;
        if (response.data && response.data.data && response.data.data.taskId) {
            taskId = response.data.data.taskId;
        } else if (response.data && response.data.taskId) {
            taskId = response.data.taskId;
        } else {
            logger.error('[Suno API] Unexpected response structure:', response.data);
            throw new Error('Invalid response from Suno API - no taskId found');
        }

        logger.info('[Suno API] Task started with ID: ' + taskId);

        // Transform response to match our expected format
        const formattedResponse = {
            data: {
                task_id: taskId,
                status: 'processing'
            },
            status: 'processing'
        };

        res.json(formattedResponse);
    } catch (error) {
        logger.error('[Music Generation] Error:', error);

        // Log more details about the error
        if (error.response) {
            logger.error('[Music Generation] API Response Error:', {
                status: error.response.status,
                data: error.response.data
            });
        }

        res.status(500).json({
            error: {
                message: error.message || 'Failed to generate music. Please try again.'
            }
        });
    }
});

// Task status endpoint
app.get('/api/music', async (req, res) => {
    const taskId = req.query.taskId;
    logger.info(`[Music Status] Checking task: ${taskId}`);

    try {
        // Check task status with Suno API
        const response = await axios.get(`https://api.sunoapi.org/api/v1/generate/record-info?taskId=${taskId}`, {
            headers: {
                'Authorization': `Bearer ${SUNO_API_KEY}`
            }
        });

        // Debug: Log full response to understand structure
        logger.debug('[Suno API] Status response:', JSON.stringify(response.data, null, 2));

        const taskData = response.data.data;
        if (!taskData) {
            logger.error('[Suno API] No task data in response:', response.data);
            throw new Error('Invalid response from Suno API - no task data');
        }

        logger.info(`[Suno API] Status for ${taskId}: ${taskData.status}`);

        // Transform Suno API status to our format
        let status;
        switch (taskData.status) {
            case 'SUCCESS':
                status = 'completed';
                break;
            case 'CREATE_TASK_FAILED':
            case 'GENERATE_AUDIO_FAILED':
            case 'CALLBACK_EXCEPTION':
            case 'SENSITIVE_WORD_ERROR':
                status = 'failed';
                break;
            case 'PENDING':
            case 'TEXT_SUCCESS':
            case 'FIRST_SUCCESS':
            default:
                status = 'processing';
                break;
        }

        const formattedResponse = {
            data: {
                task_id: taskId,
                status: status
            },
            status: status
        };

        // If generation is complete, include the audio URL
        if (status === 'completed' && taskData.response && taskData.response.sunoData && taskData.response.sunoData.length > 0) {
            try {
                // Download from Suno API and upload to DO
                logger.info('[DO] Starting upload to Digital Ocean');
                const audioUrl = taskData.response.sunoData[0].audioUrl;
                
                // Download the file from Suno API
                const audioResponse = await axios({
                    method: 'GET',
                    url: audioUrl,
                    responseType: 'stream',
                    timeout: 30000
                });
                
                // Generate unique filename
                const filename = `music_${Date.now()}_${crypto.randomBytes(4).toString('hex')}.mp3`;
                const tempPath = path.join(TEMP_DIR, filename);
                
                // Save to temp file
                await new Promise((resolve, reject) => {
                    const writer = fs.createWriteStream(tempPath);
                    audioResponse.data.pipe(writer);
                    writer.on('finish', resolve);
                    writer.on('error', reject);
                });

                // Upload to DO Spaces
                const cdnUrl = await uploadToSpaces(tempPath, 'audio/mpeg');
                
                // Cleanup temp file
                fs.unlinkSync(tempPath);
                
                logger.info(`[DO] Upload completed: ${filename}`);
                formattedResponse.data.audio_url = cdnUrl;
                
                logger.info('[Suno API] Generation completed. CDN URL:', cdnUrl);
            } catch (uploadError) {
                logger.error('[DO] Upload failed:', uploadError);
                // If upload fails, fallback to Suno API URL
                formattedResponse.data.audio_url = taskData.response.sunoData[0].audioUrl;
                logger.info('[Suno API] Falling back to Suno API URL:', taskData.response.sunoData[0].audioUrl);
            }
        }

        res.json(formattedResponse);
    } catch (error) {
        logger.error('[Music Status] Error:', error);
        res.status(500).json({
            error: {
                message: error.message || 'Failed to check music status.'
            }
        });
    }
});

// Video conversion endpoint
app.post('/api/video/convert', async (req, res) => {
    try {
        const { audioUrl } = req.body;
        
        if (!audioUrl) {
            return res.status(400).json({ 
                success: false, 
                message: 'Audio URL is required' 
            });
        }

        // Generate taskId
        const taskId = crypto.randomBytes(8).toString('hex');
        logger.info(`[Video Conversion] Starting conversion for task: ${taskId}`);

        // Check if ffmpeg is available
        const ffmpegAvailable = await checkFfmpeg();
        if (!ffmpegAvailable) {
            return res.status(500).json({ 
                success: false, 
                message: 'Video conversion is not available' 
            });
        }

        // Initialize conversion status
        updateTaskStatus(taskId, 'processing', 0, 'Starting video conversion');

        // Start conversion in background
        processAudioToVideo(audioUrl, taskId)
            .then(videoUrl => {
                updateTaskStatus(taskId, 'completed', 100, 'Video ready', videoUrl);
                logger.info(`[Video Conversion] Completed for task: ${taskId}`);
            })
            .catch(error => {
                logger.error(`[Video Conversion] Failed for task ${taskId}:`, error);
                updateTaskStatus(taskId, 'failed', 0, error.message);
            });

        // Return immediate response
        res.json({
            success: true,
            message: 'Video conversion started',
            taskId
        });

    } catch (error) {
        logger.error('[Video Conversion] Error:', error);
        res.status(500).json({ 
            success: false, 
            message: error.message 
        });
    }
});

// Video status endpoint
app.get('/api/video/status/:taskId', (req, res) => {
    try {
        const { taskId } = req.params;
        const status = taskStatus.get(taskId);
        
        if (!status) {
            return res.status(404).json({ 
                success: false, 
                message: 'Video task not found' 
            });
        }

        res.json({
            success: true,
            ...status
        });
    } catch (error) {
        logger.error('[Video Status] Error:', error);
        res.status(500).json({ 
            success: false, 
            message: error.message 
        });
    }
});

// Start server
app.listen(PORT, () => {
    logger.info(`Server running on port ${PORT}`);
});
