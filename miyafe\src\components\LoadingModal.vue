<template>
  <Transition name="modal" appear>
    <div v-if="isOpen" class="fixed inset-0 bg-gradient-to-br from-black/60 via-gray-900/70 to-black/80 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div class="relative bg-white/95 backdrop-blur-md rounded-3xl p-8 max-w-md w-full shadow-2xl border border-white/20 overflow-hidden">
        <!-- Animated Background Pattern -->
        <div class="absolute inset-0 opacity-5">
          <div class="floating-notes">
            <div v-for="n in 12" :key="n" class="note" :style="{ animationDelay: `${n * 0.5}s` }">♪</div>
          </div>
        </div>

        <!-- Main Content -->
        <div class="relative z-10">
          <!-- Central Loading Animation -->
          <div class="flex flex-col items-center mb-8">
            <!-- Vinyl Record Animation -->
            <div class="relative mb-6">
              <div class="vinyl-record">
                <div class="vinyl-center"></div>
                <div class="vinyl-groove"></div>
                <div class="vinyl-groove-2"></div>
                <div class="vinyl-groove-3"></div>
              </div>
              <!-- Needle -->
              <div class="needle"></div>
            </div>

            <!-- Pulsing Dots -->
            <div class="flex space-x-2 mb-6">
              <div v-for="n in 3" :key="n" class="pulse-dot" :style="{ animationDelay: `${n * 0.2}s` }"></div>
            </div>
          </div>

          <!-- Content Section -->
          <div class="text-center space-y-6">
            <!-- Title with Gradient -->
            <div class="space-y-2">
              <h3 class="text-2xl font-bold bg-gradient-to-r from-[#74C3C5] to-[#5ba0a2] bg-clip-text text-transparent">
                Crafting Your Masterpiece
              </h3>
              <p class="text-gray-600 text-lg font-medium transition-all duration-500">
                {{ currentMessage }}
              </p>
            </div>

            <!-- Enhanced Progress Section -->
            <div class="space-y-3">
              <!-- Progress Ring -->
              <div class="relative w-24 h-24 mx-auto">
                <svg class="w-24 h-24 transform -rotate-90" viewBox="0 0 100 100">
                  <!-- Background circle -->
                  <circle cx="50" cy="50" r="45" stroke="#e5e7eb" stroke-width="8" fill="none" />
                  <!-- Progress circle -->
                  <circle
                    cx="50"
                    cy="50"
                    r="45"
                    stroke="#74C3C5"
                    stroke-width="8"
                    fill="none"
                    stroke-linecap="round"
                    :stroke-dasharray="circumference"
                    :stroke-dashoffset="circumference - (progress / 100) * circumference"
                    class="transition-all duration-300 ease-out"
                  />
                </svg>
                <div class="absolute inset-0 flex items-center justify-center">
                  <span class="text-xl font-bold text-[#74C3C5]">{{ formatTime(timeElapsed) }}</span>
                </div>
              </div>

              <!-- Status Text -->
              <div class="flex justify-center items-center text-sm text-gray-500">
                <span>AI Processing</span>
              </div>
              
              <!-- Time Estimation Note -->
              <div class="text-center text-xs text-gray-400 mt-2">
                <p>Generation typically takes 3-4 minutes</p>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
  </Transition>
</template>

<script setup>
import { ref, onUnmounted, watch, computed } from 'vue'

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  }
})

const messages = [
  "Analyzing your creative vision...",
  "Composing melodic foundations...",
  "Layering atmospheric textures...",
  "Crafting rhythmic patterns...",
  "Applying sonic enhancement...",
  "Polishing the final arrangement...",
  "Adding that perfect lo-fi warmth...",
  "Finalizing your musical masterpiece..."
]

const currentMessage = ref(messages[0])
const messageIndex = ref(0)
const progress = ref(0)
const timeElapsed = ref(0)
let messageInterval
let progressInterval
let timeInterval

// Computed property for circle circumference
const circumference = computed(() => 2 * Math.PI * 45)

function rotateMessage() {
  messageIndex.value = (messageIndex.value + 1) % messages.length
  currentMessage.value = messages[messageIndex.value]
}

function formatTime(seconds) {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

// Watch for modal open/close
watch(() => props.isOpen, (newValue) => {
  if (newValue) {
    // Reset and start intervals when modal opens
    messageIndex.value = 0
    currentMessage.value = messages[0]
    progress.value = 0
    timeElapsed.value = 0

    messageInterval = setInterval(rotateMessage, 4000)
    progressInterval = setInterval(() => {
      if (progress.value < 85) {
        // Slower progress at the beginning, faster in the middle
        const increment = progress.value < 20 ? 0.5 : progress.value < 60 ? 1.5 : 0.8
        progress.value = Math.min(85, progress.value + increment)
      }
    }, 1000)
    timeInterval = setInterval(() => {
      timeElapsed.value += 1
    }, 1000)
  } else {
    // Clear intervals when modal closes
    clearInterval(messageInterval)
    clearInterval(progressInterval)
    clearInterval(timeInterval)
  }
})

onUnmounted(() => {
  clearInterval(messageInterval)
  clearInterval(progressInterval)
  clearInterval(timeInterval)
})
</script>

<style scoped>
/* Modal Transitions */
.modal-enter-active, .modal-leave-active {
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.modal-enter-from, .modal-leave-to {
  opacity: 0;
  transform: scale(0.9);
}

.modal-enter-active .relative {
  transition: transform 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.modal-enter-from .relative {
  transform: translateY(20px) scale(0.95);
}

/* Floating Musical Notes Background */
.floating-notes {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.note {
  position: absolute;
  font-size: 24px;
  color: #74C3C5;
  animation: float 8s infinite linear;
  opacity: 0.1;
}

.note:nth-child(1) { left: 10%; animation-duration: 6s; }
.note:nth-child(2) { left: 20%; animation-duration: 8s; }
.note:nth-child(3) { left: 30%; animation-duration: 7s; }
.note:nth-child(4) { left: 40%; animation-duration: 9s; }
.note:nth-child(5) { left: 50%; animation-duration: 6.5s; }
.note:nth-child(6) { left: 60%; animation-duration: 8.5s; }
.note:nth-child(7) { left: 70%; animation-duration: 7.5s; }
.note:nth-child(8) { left: 80%; animation-duration: 9.5s; }
.note:nth-child(9) { left: 90%; animation-duration: 6.8s; }
.note:nth-child(10) { left: 15%; animation-duration: 8.2s; }
.note:nth-child(11) { left: 35%; animation-duration: 7.8s; }
.note:nth-child(12) { left: 75%; animation-duration: 6.3s; }

@keyframes float {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 0.1;
  }
  90% {
    opacity: 0.1;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}

/* Vinyl Record Animation */
.vinyl-record {
  width: 80px;
  height: 80px;
  background: linear-gradient(45deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
  border-radius: 50%;
  position: relative;
  animation: spin 3s linear infinite;
  box-shadow:
    0 0 0 2px #333,
    0 4px 8px rgba(0,0,0,0.3),
    inset 0 0 0 1px rgba(255,255,255,0.1);
}

.vinyl-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  background: #74C3C5;
  border-radius: 50%;
  box-shadow: inset 0 0 0 2px #5ba0a2;
}

.vinyl-groove, .vinyl-groove-2, .vinyl-groove-3 {
  position: absolute;
  border: 1px solid rgba(96, 158, 175, 0.3);
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.vinyl-groove {
  width: 60px;
  height: 60px;
}

.vinyl-groove-2 {
  width: 45px;
  height: 45px;
}

.vinyl-groove-3 {
  width: 30px;
  height: 30px;
}

/* Needle */
.needle {
  position: absolute;
  top: 10px;
  right: 15px;
  width: 2px;
  height: 35px;
  background: linear-gradient(to bottom, #74C3C5, #5ba0a2);
  border-radius: 1px;
  transform-origin: bottom center;
  animation: needle-sway 4s ease-in-out infinite;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.needle::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -2px;
  width: 6px;
  height: 6px;
  background: #74C3C5;
  border-radius: 50%;
  box-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes needle-sway {
  0%, 100% { transform: rotate(-5deg); }
  50% { transform: rotate(5deg); }
}

/* Pulsing Dots */
.pulse-dot {
  width: 8px;
  height: 8px;
  background: #74C3C5;
  border-radius: 50%;
  animation: pulse-scale 1.5s ease-in-out infinite;
}

@keyframes pulse-scale {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.5);
    opacity: 0.7;
  }
}
</style>